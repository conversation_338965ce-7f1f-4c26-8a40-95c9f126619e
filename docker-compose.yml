version: '3.8'

services:
  # REST API Service (original)
  rest-api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - SESSION_SECRET=your-secret-key-here
    volumes:
      - ./database.sqlite:/app/database.sqlite
      - ./sessions.sqlite:/app/sessions.sqlite
    command: ["node", "app.js"]
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # SOAP Service
  soap-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - SOAP_PORT=3001
    volumes:
      - ./database.sqlite:/app/database.sqlite
    command: ["node", "src/soap-server.js"]
    depends_on:
      - rest-api
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/soap?wsdl"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: mantis-clone-network
